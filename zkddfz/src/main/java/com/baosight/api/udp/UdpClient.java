package com.baosight.api.udp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * UDP客户端
 * 用于将车辆实时数据发送到指定的UDP服务端
 */
@Component
public class UdpClient {

    private static final Logger logger = LoggerFactory.getLogger(UdpClient.class);

    @Value("${udp.client.target.host:127.0.0.1}")
    private String targetHost;

    @Value("${udp.client.target.port:9092}")
    private int targetPort;

    @Value("${udp.client.enabled:true}")
    private boolean clientEnabled;

    private DatagramSocket socket;
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 初始化UDP客户端
     */
    public void initialize() {
        if (initialized.get()) {
            logger.debug("UDP客户端已经初始化");
            return;
        }

        if (!clientEnabled) {
            logger.info("UDP客户端功能已禁用");
            return;
        }

        try {
            socket = new DatagramSocket();
            initialized.set(true);
            logger.info("🚀 UDP客户端初始化成功，目标服务端: {}:{}", targetHost, targetPort);
        } catch (SocketException e) {
            logger.error("UDP客户端初始化失败: {}", e.getMessage(), e);
            initialized.set(false);
        }
    }

    /**
     * 发送车辆数据到UDP服务端
     * @param vehicleData 车辆数据列表
     */
    public void sendVehicleData(List<?> vehicleData) {
        if (!initialized.get() || !clientEnabled) {
            logger.debug("UDP客户端未初始化或已禁用，跳过发送");
            return;
        }

        try {
            // 将车辆数据序列化为JSON
            String jsonData = objectMapper.writeValueAsString(vehicleData);
            sendData(jsonData);
            
            logger.debug("📤 已发送车辆数据到UDP服务端: {} 辆车", vehicleData.size());

        } catch (Exception e) {
            logger.error("发送车辆数据到UDP服务端失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送单个车辆数据到UDP服务端
     * @param vehicleData 单个车辆数据
     */
    public void sendSingleVehicleData(Object vehicleData) {
        if (!initialized.get() || !clientEnabled) {
            logger.debug("UDP客户端未初始化或已禁用，跳过发送");
            return;
        }

        try {
            // 将车辆数据序列化为JSON
            String jsonData = objectMapper.writeValueAsString(vehicleData);
            sendData(jsonData);
            
            logger.debug("📤 已发送单个车辆数据到UDP服务端");

        } catch (Exception e) {
            logger.error("发送单个车辆数据到UDP服务端失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送原始数据到UDP服务端
     * @param rawData 原始数据字符串
     */
    public void sendRawData(String rawData) {
        if (!initialized.get() || !clientEnabled) {
            logger.debug("UDP客户端未初始化或已禁用，跳过发送");
            return;
        }

        try {
            sendData(rawData);
            logger.debug("📤 已发送原始数据到UDP服务端，长度: {} 字节", rawData.length());

        } catch (Exception e) {
            logger.error("发送原始数据到UDP服务端失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送数据到目标UDP服务端
     * @param data 要发送的数据字符串
     */
    private void sendData(String data) {
        if (socket == null || socket.isClosed()) {
            logger.warn("UDP客户端socket未初始化或已关闭");
            return;
        }

        try {
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            InetAddress targetAddress = InetAddress.getByName(targetHost);
            DatagramPacket packet = new DatagramPacket(dataBytes, dataBytes.length, targetAddress, targetPort);
            
            socket.send(packet);
            
            logger.debug("📤 UDP数据已发送到 {}:{}, 长度: {} 字节", targetHost, targetPort, dataBytes.length);
        } catch (Exception e) {
            logger.error("向 {}:{} 发送UDP数据失败: {}", targetHost, targetPort, e.getMessage());
        }
    }

    /**
     * 检查UDP客户端是否已初始化
     */
    public boolean isInitialized() {
        return initialized.get();
    }

    /**
     * 检查UDP客户端是否已启用
     */
    public boolean isEnabled() {
        return clientEnabled;
    }

    /**
     * 获取目标服务端信息
     */
    public String getTargetInfo() {
        return String.format("目标服务端: %s:%d, 状态: %s", 
            targetHost, targetPort, 
            initialized.get() ? "已连接" : "未连接");
    }

    /**
     * 获取UDP客户端配置信息
     */
    public String getClientInfo() {
        return String.format("UDP客户端 - 目标: %s:%d, 启用: %s, 初始化: %s", 
            targetHost, targetPort, clientEnabled, initialized.get());
    }

    /**
     * 关闭UDP客户端
     */
    public void shutdown() {
        if (initialized.get()) {
            logger.info("🛑 正在关闭UDP客户端...");
            initialized.set(false);
            
            if (socket != null && !socket.isClosed()) {
                socket.close();
                logger.info("✅ UDP客户端已关闭");
            }
        }
    }
}
