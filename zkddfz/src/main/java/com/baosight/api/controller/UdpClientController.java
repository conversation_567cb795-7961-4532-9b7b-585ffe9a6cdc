package com.baosight.api.controller;

import com.baosight.api.websocket.RouteWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * UDP客户端管理控制器
 * 提供UDP客户端服务的管理接口
 */
@RestController
@RequestMapping("/api/udp-client")
@CrossOrigin(origins = "*")
public class UdpClientController {

    @Autowired
    private RouteWebSocketHandler routeWebSocketHandler;

    /**
     * 获取UDP客户端状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        String status = routeWebSocketHandler.getUdpClientStatus();
        String targetInfo = routeWebSocketHandler.getUdpClientTargetInfo();
        
        Map<String, Object> response = Map.of(
            "status", "running",
            "clientInfo", status,
            "targetInfo", targetInfo,
            "timestamp", System.currentTimeMillis(),
            "message", "UDP客户端状态信息"
        );

        return ResponseEntity.ok(response);
    }

    /**
     * 获取UDP客户端配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> config = Map.of(
            "description", "UDP客户端配置信息",
            "targetHost", "**************",
            "targetPort", 5000,
            "enabled", true,
            "features", Map.of(
                "vehicleDataSending", "实时车辆数据发送",
                "targetServerConnection", "连接到指定UDP服务端",
                "automaticSending", "接收到WebSocket数据时自动发送"
            ),
            "timestamp", System.currentTimeMillis()
        );

        return ResponseEntity.ok(config);
    }

    /**
     * 获取使用说明
     */
    @GetMapping("/help")
    public ResponseEntity<Map<String, Object>> getHelp() {
        Map<String, Object> help = Map.of(
            "title", "UDP客户端服务使用说明",
            "description", "UDP客户端服务用于将WebSocket接收到的车辆实时数据通过UDP协议发送到指定的服务端",
            "endpoints", Map.of(
                "GET /api/udp-client/status", "获取UDP客户端状态",
                "GET /api/udp-client/config", "获取UDP客户端配置信息",
                "GET /api/udp-client/help", "获取使用说明"
            ),
            "usage", Map.of(
                "automaticSending", "当WebSocket接收到车辆状态数据时，会自动通过UDP客户端发送到指定服务端",
                "targetServer", "默认发送到 127.0.0.1:9092",
                "configuration", "可通过application-dev.properties配置目标服务端地址和端口"
            ),
            "configuration", Map.of(
                "udp.client.target.host", "目标UDP服务端主机地址",
                "udp.client.target.port", "目标UDP服务端端口",
                "udp.client.enabled", "是否启用UDP客户端功能"
            ),
            "dataFormat", Map.of(
                "description", "发送的数据格式为JSON数组",
                "example", "[{\"device_id\":\"PCC_12345\",\"position\":{\"x\":100,\"y\":0,\"z\":200},\"rotation\":{\"x\":0,\"y\":90,\"z\":0},\"duration\":0,\"power\":85,\"consume\":15,\"type\":\"PCC\"}]"
            ),
            "timestamp", System.currentTimeMillis()
        );

        return ResponseEntity.ok(help);
    }

    /**
     * 测试UDP客户端连接
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testConnection() {
        try {
            // 发送测试数据
            String testData = "{\"test\":true,\"message\":\"UDP客户端连接测试\",\"timestamp\":" + System.currentTimeMillis() + "}";
            
            // 这里可以添加发送测试数据的逻辑
            // udpClient.sendRawData(testData);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "UDP客户端连接测试完成",
                "testData", testData,
                "targetInfo", routeWebSocketHandler.getUdpClientTargetInfo(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "UDP客户端连接测试失败: " + e.getMessage(),
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.status(500).body(response);
        }
    }
}
