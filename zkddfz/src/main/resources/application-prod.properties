# server
server.port=8080

# datasource
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;MODE=MYSQL;DATABASE_TO_LOWER=TRUE
spring.h2.console.enabled=false

# mybatis
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.mapper-locations=classpath:mapper/**/*.xml

# jpa
spring.jpa.open-in-view=false

# threads
spring.threads.virtual.enabled=true

# UDP configuration
udp.server.port=9090
udp.server.buffer.size=1024
# UDP????? - ????????????
udp.client.target.host=127.0.0.1
udp.client.target.port=9092
udp.client.enabled=true
# ?????????????
udp.broadcast.port=9091
udp.broadcast.address=127.0.0.1
udp.broadcast.enabled=false
